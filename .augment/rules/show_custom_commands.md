---
type: "always_apply"
---

when the user types "@<text>"(where "text" could be any string) in the chat box and press enter, before executing any default action, read the file '/.ai_context_eng/context_config.md', locates the Commands directory, and verify if the "text"  matches a file on that directory and if so, read the file and execute what the file content indicates.
For matching the "text" with filenames, ignore file extensions sich as ".cmd.md" or similars.