---
type: "always_apply"
---

when the user types "@<text>"(where "text" could be any word, such as "do_something") in the chat box and press enter, before executing any default action, read the file '/.ai_context_eng/context_config.md', locates the Commands directory, and verify if the "text"  matches a file on that directory and if so, read the file which name matches with the text, and execute what the file content indicates.
For matching the "text" with filenames, ignore file extensions sich as ".cmd.md" or similars.
If the "text" does not matches with any "command" file, print the message "No Custom Command detected...continuing with defaults."